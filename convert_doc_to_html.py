import mammoth

with open("眉山市房屋租赁合同范本2025版v1.0.docx", "rb") as docx_file:
    result = mammoth.convert_to_html(
        docx_file,
        convert_image=mammoth.images.inline(
            lambda image: image.alt_text or ""  # 可自定义图片处理
        ),
    )
    html = result.value
    messages = result.messages  # 转换提示/警告

with open("output.html", "w", encoding="utf-8") as f:
    f.write("<meta charset='utf-8'>\n" + html)